//! ScrollArea component for Terminus UI React-like interface
//!
//! This module provides a scrollable area component that can be used with the rsx! macro
//! and follows the Shadcn UI ScrollArea API pattern.

use crossterm::event::{Event, KeyCode};
use ratatui::prelude::Rect;
use ratatui::{
    Frame,
    layout::{Constraint, Direction},
    style::{Color, Style},
    widgets::{Borders, ListState},
};
use terminus_ui_component_macro::component;
use terminus_ui_core::{
    Callback, Children, ComponentProps, Element, FunctionalComponent, HasChildren,
    PropFieldMetadata, PropRequirements, TrySetChildren, VirtualNode, use_event, use_state,
};
use terminus_ui_props_macro::Props;

/// Props for the ScrollArea component
///
/// This follows the Shadcn UI ScrollArea API pattern:
/// ```rust
/// <ScrollArea
///     content={content_lines}
///     show_scrollbar={true}
///     on_scroll={handle_scroll}
/// >
///   // Optional children for custom content
/// </ScrollArea>
/// ```
#[derive(Pro<PERSON>, Debug, Clone)]
pub struct ScrollAreaProps {
    /// Content to display as a vector of strings (each string is a line)
    pub content: Vec<String>,
    /// Whether to show the scrollbar (default: true)
    pub show_scrollbar: Option<bool>,
    /// Whether to show as a list instead of paragraph (default: false)
    pub show_as_list: Option<bool>,
    /// Title for the scroll area block
    pub title: Option<String>,
    /// Border style for the scroll area
    pub border_style: Option<Style>,
    /// Callback called when scroll position changes
    pub on_scroll: Option<Callback<usize>>,
    /// Initial scroll position (default: 0)
    pub initial_scroll: Option<usize>,
    /// Child components to render inside the scroll area (alternative to content prop)
    #[children]
    pub children: Children,
}

/// ScrollArea component that provides scrollable content with optional scrollbar
///
/// This component manages scroll state and handles keyboard events for scrolling.
/// It can display content either as a paragraph or as a list, with optional scrollbar.
#[component(ScrollArea)]
pub fn scroll_area(props: ScrollAreaProps) -> Element {
    // Initialize scroll state
    let initial_scroll = props.initial_scroll.unwrap_or(0);
    let (scroll_position, set_scroll_position) = use_state(initial_scroll);
    let (list_state_handle, set_list_state) = use_state({
        let mut state = ListState::default();
        state.select(Some(0));
        state
    });

    let show_scrollbar = props.show_scrollbar.unwrap_or(true);
    let show_as_list = props.show_as_list.unwrap_or(false);
    let title = props.title.unwrap_or_else(|| "Content".to_string());
    let border_style = props.border_style.unwrap_or_default();

    // Get content from props
    let content = props.content;

    // Debug: Check if content is empty
    if content.is_empty() {
        return VirtualNode::widget(
            WidgetType::Block,
            BlockProps {
                title: Some("ScrollArea - No Content".to_string()),
                borders: Some(Borders::ALL),
                border_style: Some(border_style),
            },
            vec![VirtualNode::widget(
                WidgetType::Text,
                TextProps {
                    content: "No content provided to ScrollArea".to_string(),
                    style: None,
                },
                vec![],
            )],
        );
    }

    let content_len = content.len();
    let current_scroll = scroll_position.get();

    // Handle keyboard events for scrolling
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Down => {
                if show_as_list {
                    // Handle list navigation
                    set_list_state.update(|mut state| {
                        let i = match state.selected() {
                            Some(i) => {
                                if i >= content_len.saturating_sub(1) {
                                    0
                                } else {
                                    i + 1
                                }
                            }
                            None => 0,
                        };
                        state.select(Some(i));
                        state
                    });
                } else {
                    // Handle paragraph scrolling
                    let new_scroll = (current_scroll + 1).min(content_len.saturating_sub(1));
                    set_scroll_position.set(new_scroll);
                    if let Some(callback) = &props.on_scroll {
                        callback.emit(new_scroll);
                    }
                }
            }
            KeyCode::Up => {
                if show_as_list {
                    // Handle list navigation
                    set_list_state.update(|mut state| {
                        let i = match state.selected() {
                            Some(i) => {
                                if i == 0 {
                                    content_len.saturating_sub(1)
                                } else {
                                    i - 1
                                }
                            }
                            None => 0,
                        };
                        state.select(Some(i));
                        state
                    });
                } else {
                    // Handle paragraph scrolling
                    let new_scroll = current_scroll.saturating_sub(1);
                    set_scroll_position.set(new_scroll);
                    if let Some(callback) = &props.on_scroll {
                        callback.emit(new_scroll);
                    }
                }
            }
            KeyCode::PageDown => {
                if !show_as_list {
                    let new_scroll = (current_scroll + 10).min(content_len.saturating_sub(1));
                    set_scroll_position.set(new_scroll);
                    if let Some(callback) = &props.on_scroll {
                        callback.emit(new_scroll);
                    }
                }
            }
            KeyCode::PageUp => {
                if !show_as_list {
                    let new_scroll = current_scroll.saturating_sub(10);
                    set_scroll_position.set(new_scroll);
                    if let Some(callback) = &props.on_scroll {
                        callback.emit(new_scroll);
                    }
                }
            }
            _ => {}
        }
    }

    // Create the scroll area content using available widget types
    use terminus_ui_core::{BlockProps, LayoutProps, TextProps, WidgetType};

    // Create the main content as a block with the content as text
    let content_text = if show_as_list {
        // Format as a list with selection indicator
        let list_state = list_state_handle.get();
        let selected_index = list_state.selected().unwrap_or(0);

        content
            .iter()
            .enumerate()
            .map(|(i, line)| {
                if i == selected_index {
                    format!("> {}", line) // Highlight selected item
                } else {
                    format!("  {}", line)
                }
            })
            .collect::<Vec<_>>()
            .join("\n")
    } else {
        // Show content with scroll offset
        let visible_content: Vec<&String> = content.iter().skip(current_scroll).collect();
        visible_content
            .iter()
            .map(|s| s.as_str())
            .collect::<Vec<_>>()
            .join("\n")
    };

    let block_props = BlockProps {
        title: Some(title),
        borders: Some(Borders::ALL),
        border_style: Some(border_style),
    };

    let text_props = TextProps {
        content: if content_text.is_empty() {
            format!(
                "ScrollArea Debug: {} items, scroll: {}",
                content_len, current_scroll
            )
        } else {
            content_text
        },
        style: None,
    };

    if show_scrollbar && !show_as_list {
        // Create a layout with content and scrollbar indicator
        let layout_props = LayoutProps {
            direction: Some(Direction::Horizontal),
            constraints: Some(vec![Constraint::Min(0), Constraint::Length(3)]),
            margin: None,
        };

        // Main content block
        let content_block = VirtualNode::widget(
            WidgetType::Block,
            block_props,
            vec![VirtualNode::widget(WidgetType::Text, text_props, vec![])],
        );

        // Scrollbar indicator block
        let scrollbar_text = if content_len > 0 {
            let progress = (current_scroll as f32 / content_len.max(1) as f32 * 10.0) as usize;
            let mut scrollbar = ["│"; 10];
            if progress < scrollbar.len() {
                scrollbar[progress] = "█";
            }
            scrollbar.join("\n")
        } else {
            "│\n│\n│\n│\n│\n│\n│\n│\n│\n│".to_string()
        };

        let scrollbar_block_props = BlockProps {
            title: None,
            borders: Some(Borders::LEFT),
            border_style: Some(Style::default().fg(Color::DarkGray)),
        };

        let scrollbar_text_props = TextProps {
            content: scrollbar_text,
            style: Some(Style::default().fg(Color::DarkGray)),
        };

        let scrollbar_block = VirtualNode::widget(
            WidgetType::Block,
            scrollbar_block_props,
            vec![VirtualNode::widget(
                WidgetType::Text,
                scrollbar_text_props,
                vec![],
            )],
        );

        VirtualNode::widget(
            WidgetType::Layout,
            layout_props,
            vec![content_block, scrollbar_block],
        )
    } else {
        // Simple block with content
        VirtualNode::widget(
            WidgetType::Block,
            block_props,
            vec![VirtualNode::widget(WidgetType::Text, text_props, vec![])],
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_scroll_area_props_creation() {
        let content = vec!["Line 1".to_string(), "Line 2".to_string()];
        let on_scroll = Callback::from(|_pos: usize| {});
        let children = Children::new();

        let props = ScrollAreaProps {
            content: content.clone(),
            show_scrollbar: Some(true),
            show_as_list: Some(false),
            title: Some("Test ScrollArea".to_string()),
            border_style: Some(Style::default()),
            on_scroll: Some(on_scroll),
            initial_scroll: Some(0),
            children,
        };

        assert_eq!(props.content, content);
        assert_eq!(props.show_scrollbar, Some(true));
        assert_eq!(props.show_as_list, Some(false));
        assert_eq!(props.title, Some("Test ScrollArea".to_string()));
        assert_eq!(props.initial_scroll, Some(0));
    }
}
