use ratatui::{
    layout::{Constraint, Direction},
    style::Style,
    widgets::Borders,
};

/// Props for Block widget
#[derive(Debug, Clone, Default)]
pub struct BlockProps {
    pub title: Option<String>,
    pub borders: Option<Borders>,
    pub border_style: Option<Style>,
}

impl crate::ComponentProps for BlockProps {
    fn validate(&self) -> Result<(), String> {
        Ok(())
    }

    fn field_metadata() -> Vec<crate::PropFieldMetadata> {
        vec![
            crate::PropFieldMetadata {
                name: "title".to_string(),
                type_name: "Option<String>".to_string(),
                is_optional: true,
                default_value: Some("None".to_string()),
            },
            crate::PropFieldMetadata {
                name: "borders".to_string(),
                type_name: "Option<ratatui::widgets::Borders>".to_string(),
                is_optional: true,
                default_value: Some("None".to_string()),
            },
            crate::PropFieldMetadata {
                name: "border_style".to_string(),
                type_name: "Option<ratatui::style::Style>".to_string(),
                is_optional: true,
                default_value: Some("None".to_string()),
            },
        ]
    }
}

/// Props for Text widget
#[derive(Debug, Clone, Default)]
pub struct TextProps {
    pub content: String,
    pub style: Option<Style>,
}

impl crate::ComponentProps for TextProps {
    fn validate(&self) -> Result<(), String> {
        Ok(())
    }

    fn field_metadata() -> Vec<crate::PropFieldMetadata> {
        vec![
            crate::PropFieldMetadata {
                name: "content".to_string(),
                type_name: "String".to_string(),
                is_optional: false,
                default_value: Some("String::new()".to_string()),
            },
            crate::PropFieldMetadata {
                name: "style".to_string(),
                type_name: "Option<ratatui::style::Style>".to_string(),
                is_optional: true,
                default_value: Some("None".to_string()),
            },
        ]
    }
}

/// Props for Layout widget
#[derive(Debug, Clone, Default)]
pub struct LayoutProps {
    pub direction: Option<Direction>,
    pub constraints: Option<Vec<Constraint>>,
    pub margin: Option<u16>,
}

impl crate::ComponentProps for LayoutProps {
    fn validate(&self) -> Result<(), String> {
        Ok(())
    }

    fn field_metadata() -> Vec<crate::PropFieldMetadata> {
        vec![
            crate::PropFieldMetadata {
                name: "direction".to_string(),
                type_name: "Option<ratatui::layout::Direction>".to_string(),
                is_optional: true,
                default_value: Some("None".to_string()),
            },
            crate::PropFieldMetadata {
                name: "constraints".to_string(),
                type_name: "Option<Vec<ratatui::layout::Constraint>>".to_string(),
                is_optional: true,
                default_value: Some("None".to_string()),
            },
            crate::PropFieldMetadata {
                name: "margin".to_string(),
                type_name: "Option<u16>".to_string(),
                is_optional: true,
                default_value: Some("None".to_string()),
            },
        ]
    }
}

/// Props for Modal widget
#[derive(Debug, Clone, Default)]
pub struct ModalProps {
    pub open: bool,
    pub backdrop: Option<bool>,
    pub center: Option<bool>,
}

impl crate::ComponentProps for ModalProps {
    fn validate(&self) -> Result<(), String> {
        Ok(())
    }

    fn field_metadata() -> Vec<crate::PropFieldMetadata> {
        vec![
            crate::PropFieldMetadata {
                name: "open".to_string(),
                type_name: "bool".to_string(),
                is_optional: false,
                default_value: Some("false".to_string()),
            },
            crate::PropFieldMetadata {
                name: "backdrop".to_string(),
                type_name: "Option<bool>".to_string(),
                is_optional: true,
                default_value: Some("None".to_string()),
            },
            crate::PropFieldMetadata {
                name: "center".to_string(),
                type_name: "Option<bool>".to_string(),
                is_optional: true,
                default_value: Some("None".to_string()),
            },
        ]
    }
}
