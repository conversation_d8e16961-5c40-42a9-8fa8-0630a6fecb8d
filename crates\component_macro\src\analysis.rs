use crate::parsing::ComponentParams;
use syn::{FnArg, Ident, ReturnType, Type};

/// Component type classification based on parameters
#[derive(Debug, Clone, PartialEq)]
pub enum ComponentType {
    /// Basic component with props only
    Basic,
    /// Component with frame access (props + frame)
    Frame,
    /// Component with area access (props + area)
    Area,
    /// Component with both frame and area access (props + frame + area)
    FrameArea,
}

impl ComponentType {
    /// Get the string representation for code generation
    pub fn as_str(&self) -> &'static str {
        match self {
            ComponentType::Basic => "BasicComponent",
            ComponentType::Frame => "FrameComponent",
            ComponentType::Area => "AreaComponent",
            ComponentType::FrameArea => "FrameAreaComponent",
        }
    }
}

/// Grouped parameters for component generation to reduce argument count
/// This context provides comprehensive analysis and metadata for component generation
#[derive(Debug)]
pub struct ComponentGenerationContext<'a> {
    pub fn_name: &'a Ident,
    pub component_name: &'a Ident,
    pub props_type: &'a Type,
    pub attrs: &'a [syn::Attribute],
    pub vis: &'a syn::Visibility,
    pub generics: &'a syn::Generics,
    pub inputs: &'a syn::punctuated::Punctuated<FnArg, syn::token::Comma>,
    pub output: &'a ReturnType,
    pub block: &'a syn::Block,
    pub props_validation: &'a proc_macro2::TokenStream,
    pub component_type: ComponentType,
    pub metadata: ComponentMetadata,
    pub complexity: ParameterComplexity,
}

/// Parameters for creating ComponentGenerationContext to avoid too many arguments
pub struct ContextParams<'a> {
    pub params: &'a ComponentParams,
    pub fn_name: &'a Ident,
    pub component_name: &'a Ident,
    pub props_type: &'a Type,
    pub attrs: &'a [syn::Attribute],
    pub vis: &'a syn::Visibility,
    pub generics: &'a syn::Generics,
    pub inputs: &'a syn::punctuated::Punctuated<FnArg, syn::token::Comma>,
    pub output: &'a ReturnType,
    pub block: &'a syn::Block,
    pub props_validation: &'a proc_macro2::TokenStream,
}

impl<'a> ComponentGenerationContext<'a> {
    /// Create a new component generation context with grouped parameters
    pub fn new(context_params: ContextParams<'a>) -> Self {
        let component_type = ComponentAnalyzer::determine_component_type(context_params.params);
        let metadata = ComponentAnalyzer::extract_component_metadata(
            context_params.fn_name,
            context_params.params,
        );
        let complexity = ComponentAnalyzer::analyze_parameter_complexity(context_params.params);

        Self {
            fn_name: context_params.fn_name,
            component_name: context_params.component_name,
            props_type: context_params.props_type,
            attrs: context_params.attrs,
            vis: context_params.vis,
            generics: context_params.generics,
            inputs: context_params.inputs,
            output: context_params.output,
            block: context_params.block,
            props_validation: context_params.props_validation,
            component_type,
            metadata,
            complexity,
        }
    }

    /// Get the component type as a string for code generation
    pub fn component_type_str(&self) -> &'static str {
        self.component_type.as_str()
    }

    /// Generate complexity-based documentation comments
    pub fn generate_complexity_docs(&self) -> proc_macro2::TokenStream {
        match self.complexity {
            ParameterComplexity::Simple => quote::quote! {
                /// Simple component with props only.
                /// This component has minimal overhead and is suitable for frequent rendering.
            },
            ParameterComplexity::Moderate => quote::quote! {
                /// Moderate complexity component with frame or area access.
                /// This component has additional rendering capabilities but may have slightly higher overhead.
            },
            ParameterComplexity::Complex => quote::quote! {
                /// Complex component with both frame and area access.
                /// This component provides full rendering control but should be used judiciously for performance.
            },
        }
    }

    /// Generate metadata-based component documentation
    pub fn generate_metadata_docs(&self) -> proc_macro2::TokenStream {
        let name = &self.metadata.name;
        let has_frame = self.metadata.has_frame;
        let has_area = self.metadata.has_area;

        let frame_doc = if has_frame {
            "/// - Frame access: Available for advanced rendering operations"
        } else {
            ""
        };

        let area_doc = if has_area {
            "/// - Area access: Available for layout-aware rendering"
        } else {
            ""
        };

        quote::quote! {
            #[doc = concat!("Component: ", #name)]
            #[doc = ""]
            #[doc = "Features:"]
            #[doc = #frame_doc]
            #[doc = #area_doc]
        }
    }

    /// Check if generics are used and provide generic-aware code generation
    pub fn has_generics(&self) -> bool {
        !self.generics.params.is_empty()
    }

    /// Generate generic constraints for the component
    pub fn generate_generic_constraints(&self) -> proc_macro2::TokenStream {
        let generics = self.generics;
        if self.has_generics() {
            quote::quote! { #generics }
        } else {
            quote::quote! {}
        }
    }
}

/// Component analysis and classification
pub struct ComponentAnalyzer;

impl ComponentAnalyzer {
    /// Determine component type based on parameters
    pub fn determine_component_type(params: &ComponentParams) -> ComponentType {
        match (&params.frame_param, &params.area_param) {
            (Some(_), Some(_)) => ComponentType::FrameArea,
            (Some(_), None) => ComponentType::Frame,
            (None, Some(_)) => ComponentType::Area,
            (None, None) => ComponentType::Basic,
        }
    }

    /// Generate component struct name from function name
    pub fn generate_component_name(fn_name: &Ident) -> Ident {
        Ident::new(&format!("{}Component", fn_name), fn_name.span())
    }

    /// Extract component metadata for documentation and debugging
    pub fn extract_component_metadata(
        fn_name: &Ident,
        params: &ComponentParams,
    ) -> ComponentMetadata {
        ComponentMetadata {
            name: fn_name.to_string(),
            has_frame: params.frame_param.is_some(),
            has_area: params.area_param.is_some(),
        }
    }

    /// Validate component naming conventions
    pub fn validate_component_naming(fn_name: &Ident) -> Result<(), String> {
        let name = fn_name.to_string();

        // Note: We no longer enforce PascalCase for function names since users can specify
        // explicit component names using #[component(ComponentName)] syntax

        // Check for reserved names
        if is_reserved_component_name(&name) {
            return Err(format!(
                "Component name '{}' is reserved. Please choose a different name.",
                name
            ));
        }

        Ok(())
    }

    /// Analyze parameter complexity for optimization hints
    pub fn analyze_parameter_complexity(params: &ComponentParams) -> ParameterComplexity {
        let mut complexity = ParameterComplexity::Simple;

        if params.frame_param.is_some() {
            complexity = ParameterComplexity::Moderate;
        }

        if params.area_param.is_some() {
            if complexity == ParameterComplexity::Moderate {
                complexity = ParameterComplexity::Complex;
            } else {
                complexity = ParameterComplexity::Moderate;
            }
        }

        complexity
    }
}

/// Component metadata for documentation and debugging
#[derive(Debug, Clone)]
pub struct ComponentMetadata {
    pub name: String,
    pub has_frame: bool,
    pub has_area: bool,
}

/// Parameter complexity classification
#[derive(Debug, Clone, PartialEq)]
pub enum ParameterComplexity {
    /// Simple component (props only)
    Simple,
    /// Moderate complexity (props + frame OR props + area)
    Moderate,
    /// Complex component (props + frame + area)
    Complex,
}

/// Check if a component name is reserved
pub fn is_reserved_component_name(name: &str) -> bool {
    const RESERVED_NAMES: &[&str] = &[
        "Component",
        "Element",
        "VirtualNode",
        "Props",
        "Frame",
        "Rect",
        "Layout",
        "Block",
        "Text",
        "Widget",
    ];

    RESERVED_NAMES.contains(&name)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_component_type_determination() {
        let basic_params = ComponentParams {
            props_param: Some(syn::parse_quote! { MyProps }),
            frame_param: None,
            area_param: None,
        };
        assert_eq!(
            ComponentAnalyzer::determine_component_type(&basic_params),
            ComponentType::Basic
        );

        let frame_params = ComponentParams {
            props_param: Some(syn::parse_quote! { MyProps }),
            frame_param: Some("frame".to_string()),
            area_param: None,
        };
        assert_eq!(
            ComponentAnalyzer::determine_component_type(&frame_params),
            ComponentType::Frame
        );

        let area_params = ComponentParams {
            props_param: Some(syn::parse_quote! { MyProps }),
            frame_param: None,
            area_param: Some("area".to_string()),
        };
        assert_eq!(
            ComponentAnalyzer::determine_component_type(&area_params),
            ComponentType::Area
        );

        let frame_area_params = ComponentParams {
            props_param: Some(syn::parse_quote! { MyProps }),
            frame_param: Some("frame".to_string()),
            area_param: Some("area".to_string()),
        };
        assert_eq!(
            ComponentAnalyzer::determine_component_type(&frame_area_params),
            ComponentType::FrameArea
        );
    }

    #[test]
    fn test_component_naming_validation() {
        assert!(
            ComponentAnalyzer::validate_component_naming(&syn::parse_quote! { MyComponent })
                .is_ok()
        );
        assert!(
            ComponentAnalyzer::validate_component_naming(&syn::parse_quote! { myComponent })
                .is_err()
        );
        assert!(
            ComponentAnalyzer::validate_component_naming(&syn::parse_quote! { Component }).is_err()
        );
    }
}
