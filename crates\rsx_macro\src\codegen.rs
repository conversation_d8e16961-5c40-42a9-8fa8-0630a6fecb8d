use crate::analysis::{GenerationContext, OptimizationLevel};
use crate::parsing::{RsxElement, RsxProp, RsxPropValue};
use quote::{quote, quote_spanned};

/// RSX code generation engine with optimization support
pub struct RsxCodeGenerator;

impl RsxCodeGenerator {
    /// Generate code with specific context and optimization level
    pub fn generate_with_context(
        element: &RsxElement,
        context: &GenerationContext,
    ) -> proc_macro2::TokenStream {
        match element {
            RsxElement::Text { content, .. } => Self::generate_text_code(content, context),
            RsxElement::Expression { expr, .. } => Self::generate_expression_code(expr, context),
            RsxElement::Element {
                name,
                props,
                children,
                ..
            } => Self::generate_element_tag_code(name, props, children, context),
        }
    }

    /// Generate code for text nodes with analysis-driven optimization
    fn generate_text_code(content: &str, context: &GenerationContext) -> proc_macro2::TokenStream {
        let element_analysis = &context.element_analysis;

        // Use analysis to determine optimization strategy
        let should_cache = content.len() > 100 || element_analysis.has_dynamic_content;
        let should_inline = content.len() < 50 && !element_analysis.has_dynamic_content;

        if context.should_optimize("cache_static") && should_cache {
            // Cache large or dynamic text content
            quote! {
                {
                    static CACHED_TEXT: std::sync::OnceLock<String> = std::sync::OnceLock::new();
                    let text = CACHED_TEXT.get_or_init(|| #content.to_string());
                    ::terminus_ui::IntoElement::into_element(text.as_str())
                }
            }
        } else if context.should_optimize("inline_simple") && should_inline {
            // Inline simple text for performance
            quote! {
                ::terminus_ui::IntoElement::into_element(#content)
            }
        } else {
            // Use standard text creation with IntoElement trait
            quote! {
                ::terminus_ui::IntoElement::into_element(#content)
            }
        }
    }

    /// Generate code for expressions with analysis-driven optimization
    fn generate_expression_code(
        expr: &syn::Expr,
        context: &GenerationContext,
    ) -> proc_macro2::TokenStream {
        let element_analysis = &context.element_analysis;
        let tree_analysis = &context.tree_analysis;

        // Use analysis to determine if expression caching is beneficial
        let should_cache = tree_analysis.dynamic_elements > 5
            || element_analysis.complexity == crate::analysis::ComplexityLevel::Dynamic;

        if context.should_optimize("cache_expressions") && should_cache {
            // Add caching for expensive expressions in complex trees
            quote! {
                {
                    let _cached_result = #expr;
                    ::terminus_ui::IntoElement::into_element(_cached_result)
                }
            }
        } else if context.should_optimize("inline_simple")
            && element_analysis.complexity == crate::analysis::ComplexityLevel::Simple
        {
            // Inline simple expressions for performance
            quote! {
                ::terminus_ui::IntoElement::into_element(#expr)
            }
        } else {
            quote! {
                ::terminus_ui::IntoElement::into_element(#expr)
            }
        }
    }

    /// Generate code for element tags with analysis-driven optimization
    fn generate_element_tag_code(
        name: &syn::Path,
        props: &[RsxProp],
        children: &[RsxElement],
        context: &GenerationContext,
    ) -> proc_macro2::TokenStream {
        let element_analysis = &context.element_analysis;
        let tree_analysis = &context.tree_analysis;

        // Use analysis to optimize children generation
        let children_code = Self::generate_children_code_with_analysis(
            children,
            context,
            element_analysis,
            tree_analysis,
        );

        // Get the element name as a string
        let name_str = if name.segments.len() == 1 {
            name.segments.first().unwrap().ident.to_string()
        } else {
            quote! { #name }.to_string()
        };

        // Check if this is a built-in widget or custom component
        if let Some((widget_type, props_type)) = Self::get_widget_type(&name_str) {
            Self::generate_widget_code_with_analysis(
                &widget_type,
                &props_type,
                props,
                children_code,
                context,
                element_analysis,
            )
        } else {
            Self::generate_component_code_with_analysis(
                &name_str,
                props,
                children,
                children_code,
                context,
                element_analysis,
            )
        }
    }

    /// Generate code for widget props using typed props structs
    fn generate_widget_props_code(
        props: &[RsxProp],
        props_type: &proc_macro2::TokenStream,
        context: &GenerationContext,
    ) -> proc_macro2::TokenStream {
        if props.is_empty() {
            if context.should_optimize("cache_static") {
                // Cache static default props
                quote! {
                    {
                        static DEFAULT_PROPS: std::sync::OnceLock<#props_type> = std::sync::OnceLock::new();
                        DEFAULT_PROPS.get_or_init(|| #props_type::default()).clone()
                    }
                }
            } else {
                quote! { #props_type::default() }
            }
        } else {
            let prop_assignments = props.iter().map(|prop| {
                let name = prop.name.clone();
                match &prop.value {
                    RsxPropValue::Literal { value, .. } => {
                        // For string literals, check if the field expects Option<String> or String
                        match prop.name.to_string().as_str() {
                            "content" => quote! { #name: #value.to_string() }, // TextProps.content is String
                            _ => quote! { #name: Some(#value.to_string()) }, // Most other fields are Option<T>
                        }
                    }
                    RsxPropValue::Expression { expr, .. } => {
                        // For expressions, check if the field expects Option<T> or T
                        // Also handle callback props specially
                        match prop.name.to_string().as_str() {
                            "content" => quote! { #name: #expr.to_string() }, // TextProps.content is String
                            name if name.starts_with("on_") => {
                                // Callback props - convert to Callback if needed
                                quote! { #name: ::terminus_ui::IntoCallback::into_callback(#expr) }
                            }
                            _ => quote! { #name: Some(#expr) }, // Most other fields are Option<T>
                        }
                    }
                }
            });

            // Determine if we need ..Default::default() based on the props type and provided props
            let prop_names: std::collections::HashSet<String> =
                props.iter().map(|p| p.name.to_string()).collect();
            let needs_default = Self::needs_default_props(props_type, &prop_names);

            if needs_default {
                quote! {
                    #props_type {
                        #(#prop_assignments),*,
                        ..Default::default()
                    }
                }
            } else {
                quote! {
                    #props_type {
                        #(#prop_assignments),*
                    }
                }
            }
        }
    }

    /// Generate code for custom component props using Yew's exact approach
    fn generate_custom_component_props_code(
        props: &[RsxProp],
        component_name: &str,
        _context: &GenerationContext,
    ) -> proc_macro2::TokenStream {
        let component_ident = syn::Ident::new(component_name, proc_macro2::Span::call_site());
        let component_span = proc_macro2::Span::call_site();

        // Use Yew's exact pattern: qualified path in type annotation context (which works!)
        let props_ty = quote_spanned!(component_span=> <#component_ident as ::terminus_ui::FunctionalComponent>::Properties);

        // Yew's validation pattern (works because it's in function parameter context)
        let props_ident = syn::Ident::new("__terminus_props", component_span);
        let check_props: proc_macro2::TokenStream = props
            .iter()
            .map(|prop| {
                let prop_name = prop.name.clone();
                quote_spanned! {component_span=>
                    let _ = &__terminus_props.#prop_name;
                }
            })
            .collect();

        let validate_props = quote_spanned! {component_span=>
            #[allow(clippy::no_effect)]
            if false {
                let _ = |#props_ident: #props_ty| {
                    #check_props
                };
            };
        };

        // Yew's approach: use type annotation instead of struct initialization
        let build_props = if props.is_empty() {
            quote_spanned! {component_span=>
                {
                    // Use type annotation context (works!) instead of struct initialization
                    let props: #props_ty = Default::default();
                    props
                }
            }
        } else {
            // Generate individual field assignments
            let field_assignments = props.iter().map(|prop| {
                let name = prop.name.clone();
                let value = match &prop.value {
                    RsxPropValue::Literal { value, .. } => {
                        quote! { #value.to_string() }
                    }
                    RsxPropValue::Expression { expr, .. } => {
                        // Handle callback props specially
                        if prop.name.to_string().starts_with("on_") {
                            quote! { ::terminus_ui::IntoCallback::into_callback(#expr) }
                        } else {
                            quote! { #expr }
                        }
                    }
                };
                quote! { props.#name = #value; }
            });

            quote_spanned! {component_span=>
                {
                    // Use type annotation context (works!) with Default and field updates
                    let mut props: #props_ty = Default::default();
                    #(#field_assignments)*
                    props
                }
            }
        };

        quote! {
            {
                #validate_props
                #build_props
            }
        }
    }

    /// Get widget type information for built-in widgets
    fn get_widget_type(name: &str) -> Option<(proc_macro2::TokenStream, proc_macro2::TokenStream)> {
        match name {
            "Block" => Some((
                quote! { ::terminus_ui::WidgetType::Block },
                quote! { ::terminus_ui::BlockProps },
            )),
            "Text" => Some((
                quote! { ::terminus_ui::WidgetType::Text },
                quote! { ::terminus_ui::TextProps },
            )),
            "Layout" => Some((
                quote! { ::terminus_ui::WidgetType::Layout },
                quote! { ::terminus_ui::LayoutProps },
            )),
            _ => None,
        }
    }

    /// Check if default props are needed
    fn needs_default_props(
        props_type: &proc_macro2::TokenStream,
        prop_names: &std::collections::HashSet<String>,
    ) -> bool {
        match props_type.to_string().replace(" ", "").as_str() {
            "::terminus_ui::BlockProps" => {
                // BlockProps has: title, borders, border_style (all Option<T>)
                !(prop_names.contains("title")
                    && prop_names.contains("borders")
                    && prop_names.contains("border_style"))
            }
            "::terminus_ui::TextProps" => {
                // TextProps has: content (String), style (Option<Style>)
                !(prop_names.contains("content") && prop_names.contains("style"))
            }
            "::terminus_ui::LayoutProps" => {
                // LayoutProps has: direction, constraints, margin (all Option<T>)
                !(prop_names.contains("direction")
                    && prop_names.contains("constraints")
                    && prop_names.contains("margin"))
            }
            _ => true, // For unknown types, always include default
        }
    }

    /// Generate code for built-in widgets with analysis-driven optimization
    fn generate_widget_code_with_analysis(
        widget_type: &proc_macro2::TokenStream,
        props_type: &proc_macro2::TokenStream,
        props: &[RsxProp],
        children_code: proc_macro2::TokenStream,
        context: &GenerationContext,
        element_analysis: &crate::analysis::ElementAnalysis,
    ) -> proc_macro2::TokenStream {
        let props_code = Self::generate_widget_props_code_with_analysis(
            props,
            props_type,
            context,
            element_analysis,
        );

        quote! {
            VirtualNode::widget(
                #widget_type,
                #props_code,
                #children_code
            )
        }
    }

    /// Generate code for custom components with analysis-driven optimization
    fn generate_component_code_with_analysis(
        name_str: &str,
        props: &[RsxProp],
        children: &[RsxElement],
        children_code: proc_macro2::TokenStream,
        context: &GenerationContext,
        element_analysis: &crate::analysis::ElementAnalysis,
    ) -> proc_macro2::TokenStream {
        let component_name = syn::Ident::new(name_str, proc_macro2::Span::call_site());

        if children.is_empty() {
            // No children - use standard props generation
            let props_code = Self::generate_custom_component_props_code_with_analysis(
                props,
                name_str,
                context,
                element_analysis,
            );

            quote! {
                #component_name::create_typed_element(#props_code)
            }
        } else {
            // Has children - try automatic children detection
            Self::generate_component_with_children_detection(
                &component_name,
                props,
                children,
                children_code,
                name_str,
                context,
                element_analysis,
            )
        }
    }

    /// Generate component code with automatic children detection
    fn generate_component_with_children_detection(
        component_name: &syn::Ident,
        props: &[RsxProp],
        _children: &[RsxElement],
        children_code: proc_macro2::TokenStream,
        name_str: &str,
        context: &GenerationContext,
        element_analysis: &crate::analysis::ElementAnalysis,
    ) -> proc_macro2::TokenStream {
        // Generate props code that includes children handling
        let props_code = Self::generate_custom_component_props_code_with_children_support(
            props,
            children_code.clone(),
            name_str,
            context,
            element_analysis,
        );

        // Always use create_typed_element since children are handled in props generation
        quote! {
            #component_name::create_typed_element(#props_code)
        }
    }

    /// Generate custom component props code with automatic children support
    fn generate_custom_component_props_code_with_children_support(
        props: &[RsxProp],
        children_code: proc_macro2::TokenStream,
        component_name: &str,
        _context: &GenerationContext,
        _element_analysis: &crate::analysis::ElementAnalysis,
    ) -> proc_macro2::TokenStream {
        let component_ident = syn::Ident::new(component_name, proc_macro2::Span::call_site());
        let component_span = proc_macro2::Span::call_site();

        // Use Yew's exact pattern: qualified path in type annotation context
        let props_ty = quote_spanned!(component_span=> <#component_ident as ::terminus_ui::FunctionalComponent>::Properties);

        // Generate individual field assignments for explicit props
        let field_assignments = props.iter().map(|prop| {
            let name = prop.name.clone();
            let value = match &prop.value {
                RsxPropValue::Literal { value, .. } => {
                    quote! { #value.to_string() }
                }
                RsxPropValue::Expression { expr, .. } => {
                    if prop.name.to_string().starts_with("on_") {
                        quote! { ::terminus_ui::IntoCallback::into_callback(#expr) }
                    } else {
                        quote! { #expr }
                    }
                }
            };
            quote! { props.#name = #value; }
        });

        // Build props with automatic children detection
        quote_spanned! {component_span=>
            {
                // Create base props with provided properties
                let mut props: #props_ty = Default::default();

                // Set explicit props
                #(#field_assignments)*

                // Try to set children automatically if the component supports it
                let children_vec: Vec<::terminus_ui::Element> = #children_code;
                let children_obj = ::terminus_ui::Children::from_vec(children_vec);

                // Use a helper trait to try setting children
                props.try_set_children(children_obj);

                props
            }
        }
    }

    /// Generate code for widget props with analysis-driven optimization
    fn generate_widget_props_code_with_analysis(
        props: &[RsxProp],
        props_type: &proc_macro2::TokenStream,
        context: &GenerationContext,
        element_analysis: &crate::analysis::ElementAnalysis,
    ) -> proc_macro2::TokenStream {
        // Use analysis to determine caching strategy
        let should_cache_static = !element_analysis.has_dynamic_content && props.is_empty();

        if props.is_empty() {
            if context.should_optimize("cache_static") && should_cache_static {
                // Cache static default props for better performance
                quote! {
                    {
                        static DEFAULT_PROPS: std::sync::OnceLock<#props_type> = std::sync::OnceLock::new();
                        DEFAULT_PROPS.get_or_init(|| #props_type::default()).clone()
                    }
                }
            } else {
                quote! { #props_type::default() }
            }
        } else {
            // Use the existing prop generation logic
            Self::generate_widget_props_code(props, props_type, context)
        }
    }

    /// Generate code for custom component props with analysis-driven optimization
    fn generate_custom_component_props_code_with_analysis(
        props: &[RsxProp],
        component_name: &str,
        context: &GenerationContext,
        element_analysis: &crate::analysis::ElementAnalysis,
    ) -> proc_macro2::TokenStream {
        // Use analysis to determine optimization strategy
        let should_cache_static = !element_analysis.has_dynamic_content && props.is_empty();

        if should_cache_static && context.should_optimize("cache_static") {
            // Use Yew-style trait-based props type resolution for caching too
            let component_ident = syn::Ident::new(component_name, proc_macro2::Span::call_site());
            let props_type = quote! {
                <#component_ident as ::terminus_ui::FunctionalComponent>::Properties
            };

            quote! {
                {
                    static DEFAULT_PROPS: std::sync::OnceLock<#props_type> = std::sync::OnceLock::new();
                    DEFAULT_PROPS.get_or_init(|| <#props_type>::default()).clone()
                }
            }
        } else {
            // Use the existing prop generation logic
            Self::generate_custom_component_props_code(props, component_name, context)
        }
    }

    /// Generate code for children with analysis-driven optimization
    fn generate_children_code_with_analysis(
        children: &[RsxElement],
        context: &GenerationContext,
        element_analysis: &crate::analysis::ElementAnalysis,
        tree_analysis: &crate::analysis::TreeAnalysis,
    ) -> proc_macro2::TokenStream {
        if children.is_empty() {
            // Use analysis to determine if static caching is beneficial
            let should_cache =
                !element_analysis.has_dynamic_content && tree_analysis.total_elements > 10;

            if context.should_optimize("cache_static") && should_cache {
                quote! {
                    {
                        static EMPTY_CHILDREN: &[::terminus_ui::Element] = &[];
                        EMPTY_CHILDREN.to_vec()
                    }
                }
            } else {
                quote! { vec![] }
            }
        } else {
            let child_codes = children
                .iter()
                .map(|child| Self::generate_with_context(child, context));

            // Use analysis to determine batching strategy
            let should_batch = children.len() > 10 || tree_analysis.max_depth > 5;

            if context.should_optimize("batch_children") && should_batch {
                // Batch large numbers of children for performance
                let children_len = children.len();
                quote! {
                    {
                        let mut children = Vec::with_capacity(#children_len);
                        #(children.push(#child_codes);)*
                        children
                    }
                }
            } else {
                quote! {
                    vec![#(#child_codes),*]
                }
            }
        }
    }
}

/// Code generation utilities
pub struct CodeGenUtils;

impl CodeGenUtils {
    /// Generate debug-friendly code with extra information
    pub fn generate_debug(element: &RsxElement) -> proc_macro2::TokenStream {
        let context = GenerationContext::new(element, OptimizationLevel::Debug);
        let base_code = RsxCodeGenerator::generate_with_context(element, &context);

        // Add debug information
        quote! {
            {
                // Debug: RSX element generated with enhanced macro
                #base_code
            }
        }
    }
}
