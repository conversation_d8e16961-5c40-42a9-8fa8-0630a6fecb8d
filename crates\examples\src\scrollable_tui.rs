// scrollable_tui.rs - A React-like scrollable CLI output demonstration using ScrollArea component

use crossterm::event::{Event, KeyCode};
use terminus_ui::prelude::*;

/// Props for the main scrollable demo app
#[derive(Pro<PERSON>, Debug, Clone)]
pub struct ScrollableDemoAppProps {
    pub title: String,
    pub content: Vec<String>,
}

/// Main scrollable demo app component
#[component(ScrollableDemoApp)]
fn scrollable_demo_app(props: ScrollableDemoAppProps) -> Element {
    // State for toggling between paragraph and list view
    let (show_as_list, set_show_as_list) = use_state(false);
    let (scroll_position, set_scroll_position) = use_state(0usize);

    // Handle global keyboard events
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('q') => {
                // Exit the application (this would need to be handled by the main loop)
                // For now, we'll just ignore it as the component can't exit the app directly
            }
            KeyCode::Char('l') => {
                // Toggle between list and paragraph view
                set_show_as_list.update(|prev| !prev);
            }
            _ => {
                // Other keys are handled by the ScrollArea component
            }
        }
    }

    let current_show_as_list = show_as_list.get();
    let current_scroll = scroll_position.get();

    // Callback for scroll position changes
    let on_scroll = Callback::from(move |new_position: usize| {
        set_scroll_position.set(new_position);
    });

    rsx! {
        <Layout direction={Direction::Vertical} constraints={vec![
            Constraint::Min(1),     // Main content area
            Constraint::Length(1),  // Footer
        ]}>
            // Main scrollable content area
            <ScrollArea
                content={props.content.clone()}
                show_scrollbar={true}
                show_as_list={current_show_as_list}
                title={props.title.clone()}
                border_style={Style::default().fg(Color::Cyan)}
                on_scroll={on_scroll}
                initial_scroll={current_scroll}
            />

            // Footer with instructions
            <Block borders={Borders::TOP}>
                <Text content="↑/↓: Scroll  l: Toggle List  q: Quit" />
            </Block>
        </Layout>
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Create sample content
    let content = (1..=100)
        .map(|i| {
            format!(
                "Line {}: Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
                i
            )
        })
        .collect();

    let app_props = ScrollableDemoAppProps {
        title: "📜 Scrollable Content Demo".to_string(),
        content,
    };

    let element = rsx! {
        <ScrollableDemoApp title={app_props.title} content={app_props.content} />
    };

    render(element)
}
