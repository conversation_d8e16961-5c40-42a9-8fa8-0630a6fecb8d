// scrollable_tui.rs - A Ratatui-based scrollable CLI output demonstration

use crossterm::{
    event::{self, Event, KeyCode},
    execute,
    terminal::{EnterAlternateScreen, LeaveAlternateScreen, disable_raw_mode, enable_raw_mode},
};
use std::{
    io::{self, stdout},
    time::Duration,
};
use terminus_ui::prelude::*;

// Main application state
struct App {
    content: Vec<String>,
    scroll: usize,
    list_state: ListState,
    show_list: bool,
}

impl App {
    fn new(content: Vec<String>) -> Self {
        let mut list_state = ListState::default();
        list_state.select(Some(0));
        Self {
            content,
            scroll: 0,
            list_state,
            show_list: false,
        }
    }

    fn next_item(&mut self) {
        let i = match self.list_state.selected() {
            Some(i) => {
                if i >= self.content.len() - 1 {
                    0
                } else {
                    i + 1
                }
            }
            None => 0,
        };
        self.list_state.select(Some(i));
    }

    fn previous_item(&mut self) {
        let i = match self.list_state.selected() {
            Some(i) => {
                if i == 0 {
                    self.content.len() - 1
                } else {
                    i - 1
                }
            }
            None => 0,
        };
        self.list_state.select(Some(i));
    }

    fn scroll_down(&mut self, amount: usize) {
        self.scroll = self.scroll.saturating_add(amount);
        if self.scroll > self.content.len().saturating_sub(1) {
            self.scroll = self.content.len().saturating_sub(1);
        }
    }

    fn scroll_up(&mut self, amount: usize) {
        self.scroll = self.scroll.saturating_sub(amount);
    }
}

// Main rendering function
fn ui(frame: &mut Frame, app: &mut App) {
    let size = frame.area();

    // Create two vertical chunks
    let chunks = Layout::default()
        .direction(Direction::Vertical)
        .constraints([Constraint::Min(1), Constraint::Length(1)].as_ref())
        .split(size);

    // Main content area
    let content_area = chunks[0];
    let footer_area = chunks[1];

    if app.show_list {
        // List view
        let items: Vec<ListItem> = app
            .content
            .iter()
            .map(|item| {
                let lines = vec![Line::from(item.as_str())];
                ListItem::new(lines).style(Style::default())
            })
            .collect();

        let list = List::new(items)
            .block(Block::default().borders(Borders::ALL).title("Content"))
            .highlight_style(
                Style::default()
                    .bg(Color::DarkGray)
                    .add_modifier(Modifier::BOLD),
            );

        frame.render_stateful_widget(list, content_area, &mut app.list_state);
    } else {
        // Paragraph view with scroll
        let visible_content: Vec<&str> = app.content.iter().map(|s| s.as_str()).collect();
        let paragraph = Paragraph::new(visible_content.join("\n"))
            .block(Block::default().borders(Borders::ALL).title("Content"))
            .wrap(Wrap { trim: true })
            .scroll((app.scroll as u16, 0));

        frame.render_widget(paragraph, content_area);

        // Scrollbar
        let scrollbar = Scrollbar::default()
            .orientation(ScrollbarOrientation::VerticalRight)
            .begin_symbol(Some("↑"))
            .end_symbol(Some("↓"));

        let mut scrollbar_state = ScrollbarState::new(app.content.len())
            .position(app.scroll)
            .viewport_content_length(content_area.height as usize);
        frame.render_stateful_widget(scrollbar, content_area, &mut scrollbar_state);
    }

    // Footer with instructions
    let footer = Paragraph::new(Text::from(vec![Line::from(vec![
        Span::styled("↑/↓", Style::default().add_modifier(Modifier::BOLD)),
        Span::raw(": Scroll  "),
        Span::styled("l", Style::default().add_modifier(Modifier::BOLD)),
        Span::raw(": Toggle List  "),
        Span::styled("q", Style::default().add_modifier(Modifier::BOLD)),
        Span::raw(": Quit"),
    ])]))
    .block(Block::default().borders(Borders::TOP));

    frame.render_widget(footer, footer_area);
}

// Main application loop
fn run_app(
    terminal: &mut Terminal<CrosstermBackend<std::io::Stdout>>,
    mut app: App,
) -> io::Result<()> {
    loop {
        terminal.draw(|f| ui(f, &mut app))?;

        if event::poll(Duration::from_millis(100))? {
            if let Event::Key(key) = event::read()? {
                match key.code {
                    KeyCode::Char('q') => return Ok(()),
                    KeyCode::Char('l') => app.show_list = !app.show_list,
                    KeyCode::Down => {
                        if app.show_list {
                            app.next_item();
                        } else {
                            app.scroll_down(1);
                        }
                    }
                    KeyCode::Up => {
                        if app.show_list {
                            app.previous_item();
                        } else {
                            app.scroll_up(1);
                        }
                    }
                    KeyCode::PageDown => app.scroll_down(10),
                    KeyCode::PageUp => app.scroll_up(10),
                    _ => {}
                }
            }
        }
    }
}

// Main function
fn main() -> io::Result<()> {
    // Setup terminal
    enable_raw_mode()?;
    let mut stdout = stdout();
    execute!(stdout, EnterAlternateScreen)?;
    let backend = CrosstermBackend::new(stdout);
    let mut terminal = Terminal::new(backend)?;

    // Create sample content
    let content = (1..=100)
        .map(|i| {
            format!(
                "Line {}: Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
                i
            )
        })
        .collect();

    // Create app and run it
    let app = App::new(content);
    let res = run_app(&mut terminal, app);

    // Cleanup terminal
    disable_raw_mode()?;
    execute!(terminal.backend_mut(), LeaveAlternateScreen)?;

    res
}
